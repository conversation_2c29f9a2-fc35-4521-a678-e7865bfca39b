import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { extractDailySequence } from '@/lib/db/v4/operations/order-ops';
import { barcodeService } from '@/lib/services/barcode-service';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';
import { getOrderTypeLabel } from '@/lib/types/order-types';

// 🍽️ Kitchen Printing Systems Service
// Implements the 3 systems from the implementation plan

export type PrintingSystem = 'single' | 'multi-station' | 'multi-barcode';

export interface PrinterConfig {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[]; // Categories assigned to this printer (station)
  type: 'thermal' | 'inkjet' | 'laser';
  simulated: boolean;
  isReceiptPrinter?: boolean; // 🆕 Flag to identify receipt printers
}

export interface PrintJob {
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  printerId?: string;
  stationName?: string;
}

export interface PrintResult {
  success: boolean;
  printJob?: PrintJob;

  printJobs?: PrintJob[];  // For multi-station systems
  showPreview?: boolean;
  error?: string;
}

export interface ItemStatus {
  orderId: string;
  itemId: string;
  itemName: string;
  status: 'pending' | 'done';
  scannedAt?: string;
  stationId?: string;
  createdAt?: string;
}

interface OSPrinterInput {
  id: string;
  name: string;
  ipAddress?: string;
  status?: 'online' | 'offline' | 'unknown';
  type?: 'thermal' | 'inkjet' | 'laser';
}

import { savePrinterSettings, loadPrinterSettings } from '@/lib/db/v4/operations/printer-settings-ops';

class KitchenPrintService {
  private currentSystem: PrintingSystem = 'single';
  private printers: PrinterConfig[] = [];
  private itemStatuses: Map<string, ItemStatus> = new Map();
  
  // 🎯 System Configuration
  setSystem(system: PrintingSystem) {
    this.currentSystem = system;
    localStorage.setItem('kitchen_printing_system', system);
  }
  
  getSystem(): PrintingSystem {
    const stored = localStorage.getItem('kitchen_printing_system');
    const system = (stored as PrintingSystem) || 'multi-barcode'; // Default to multi-barcode for testing with barcodes
    
    // Store the default if nothing was stored
    if (!stored) {
      this.setSystem(system);
    }
    
    return system;
  }
  
  async setPrinters(printers: PrinterConfig[]) {
    this.printers = printers;
    await savePrinterSettings(printers);
  }
  
  async getPrinters(): Promise<PrinterConfig[]> {
    const printers = await loadPrinterSettings();
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         typeof window !== 'undefined' && window.location.hostname === 'localhost';
    const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

    // 🎯 DEVELOPMENT MODE: Always ensure mock printers are available
    if (isDevelopment) {
      // If no printers exist, create mock ones
      if (printers.length === 0) {
        console.log('🧪 [DEVELOPMENT] No printers found, creating mock printers for testing...');
        await this.assignRealCategoriesToPrinters();
        return this.printers;
      }

      // If printers exist but are all simulated and we have no real categories assigned, refresh them
      const allSimulated = printers.every(p => p.simulated);
      if (allSimulated) {
        const hasAssignments = printers.some(p => p.assignedCategories.length > 0);
        if (!hasAssignments) {
          console.log('🧪 [DEVELOPMENT] Refreshing mock printer category assignments...');
          await this.assignRealCategoriesToPrinters();
          return this.printers;
        }
      }

      // Return existing printers (mix of real and simulated)
      return printers;
    }

    // 🎯 PRODUCTION MODE: Only real printers
    if (printers.length === 0) {
      if (isElectron) {
        console.log('🚀 [PRODUCTION] No printers found or configured. Use the settings page to discover and set up printers.');
      } else {
        console.log('🌐 [PRODUCTION WEB] No printers configured. Kitchen printing requires Electron app.');
      }
      return [];
    }

    return printers;
  }
  
  // 🆕 Get receipt printer ID from configuration
  getReceiptPrinterId(): string | null {
    const receiptPrinter = this.printers.find(p => p.isReceiptPrinter && p.status === 'online');
    return receiptPrinter?.id || null;
  }
  
  // 🆕 Set receipt printer by ID
  async setReceiptPrinter(printerId: string | null) {
    this.printers = this.printers.map(printer => ({
      ...printer,
      isReceiptPrinter: printer.id === printerId
    }));
    await this.setPrinters(this.printers);
  }
  
  // 🏭 Initialize default printers for testing (DEVELOPMENT MODE ONLY - NEVER IN PRODUCTION)
  private getDefaultPrinters(): PrinterConfig[] {
    // 🚨 CRITICAL PRODUCTION SAFETY CHECK
    const isProduction = process.env.NODE_ENV === 'production';
    const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         typeof window !== 'undefined' && window.location.hostname === 'localhost';

    if (isProduction && isElectron) {
      console.error('🚨 PRODUCTION SAFETY: Mock printers blocked in production Electron build');
      throw new Error('Mock printers are not allowed in production builds');
    }

    if (!isDevelopment) {
      console.warn('🚨 SAFETY: Mock printers only available in development mode');
      return [];
    }

    // 🚨 WARNING: These are SIMULATED printers for development testing only!
    console.warn('🧪 Creating MOCK printers for development - these should NEVER appear in production!');

    return [
      {
        id: 'dev-mock-main-kitchen',
        name: '🧪 [DEV] Mock Kitchen Printer',
        status: 'online',
        assignedCategories: [], // Will be populated with real category IDs
        type: 'thermal',
        simulated: true // This flag indicates it's a development mock
      }
    ];
  }
  
  // 🎯 Auto-assign real categories to simulated printers
  async assignRealCategoriesToPrinters(): Promise<void> {
    // 🚨 PRODUCTION SAFETY CHECK
    const isProduction = process.env.NODE_ENV === 'production';
    const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         typeof window !== 'undefined' && window.location.hostname === 'localhost';

    if (isProduction && isElectron) {
      console.error('🚨 PRODUCTION SAFETY: Mock printer assignment blocked in production');
      throw new Error('Mock printer assignment is not allowed in production builds');
    }

    if (!isDevelopment) {
      console.log('🚨 SAFETY: Mock printer assignment only available in development mode');
      return;
    }

    try {
      // Import menu operations to get real categories
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      
      if (!menu.categories || menu.categories.length === 0) {
        console.log('📝 No real categories found, creating basic mock printer');

        // Create a basic mock printer even without categories
        const basicPrinter: PrinterConfig = {
          id: 'dev-mock-basic',
          name: '🧪 [DEV] Basic Mock Printer',
          status: 'online',
          assignedCategories: [],
          type: 'thermal',
          simulated: true
        };

        this.printers = [basicPrinter];
        await this.setPrinters(this.printers);
        console.log('✅ Created basic mock printer (no categories available)');
        return;
      }
      
      const categoryIds = menu.categories.map(cat => cat.id);
      const numCategories = categoryIds.length;
      
      // 🎯 Create printers dynamically based on actual categories
      const dynamicPrinters: PrinterConfig[] = [];
      
      categoryIds.forEach((categoryId, index) => {
        const category = menu.categories.find(c => c.id === categoryId);
        const categoryName = category?.name || categoryId;
        
        dynamicPrinters.push({
          id: `station-${categoryId}`,
          name: `${categoryName} Station`,
          status: 'online',
          assignedCategories: [categoryId], // Each printer gets exactly one category
          type: 'thermal',
          simulated: true
        });
        
        console.log(`🖨️ Created printer for ${categoryName} (${categoryId})`);
      });
      
      // Save the dynamically created printers
      this.setPrinters(dynamicPrinters);
      this.printers = dynamicPrinters;
      
      console.log(`🎯 Created ${numCategories} printers for ${numCategories} categories`);
      
    } catch (error) {
      console.warn('⚠️ Could not assign real categories to printers:', error);
      // Fallback: keep minimal default printer
      const defaultPrinters = this.getDefaultPrinters();
      this.setPrinters(defaultPrinters);
      this.printers = defaultPrinters;
    }
  }
  
  // 🎯 Generate simple but reliable barcode ID for kitchen items
  private generateSimpleBarcodeId(orderId: string, itemIndex: number): string {
    // 🎯 COMPACT FORMAT requested by user
    // - Omit year and any non-numeric characters to shorten stripes
    // - Use the *daily sequence* of the order (3 digits) + a 3-digit 1-based
    //   item instance index. Example: dailySequence "002", item #5 ➜ "002005"
    //   This is at most 6 digits, fully numeric → fewer CODE128 bars.

    const dailySeq = extractDailySequence(orderId); // already 3-digit string
    const idxStr = itemIndex.toString().padStart(3, '0');
    return `${dailySeq}${idxStr}`; // e.g. 002005
  }

  // 🎨 Get font sizes based on option
  private getFontSizes(fontSize?: 'small' | 'medium' | 'large') {
    const fontSizeOption = fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    return fontSizes[fontSizeOption];
  }
  
  // 🖨️ Main Print Function
  async printKitchenOrder(
    order: Order,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    try {
      this.currentSystem = this.getSystem();
      this.printers = await this.getPrinters();

      console.log('🖨️ [printKitchenOrder] Current system:', this.currentSystem);
      console.log('🖨️ [printKitchenOrder] Available printers:', this.printers.length);

      // 🚨 SAFETY CHECK: No printers configured
      if (!this.printers || this.printers.length === 0) {
        console.warn('[KitchenPrintService] No printers configured. Aborting print.');

        // In development, try to create mock printers
        const isDevelopment = process.env.NODE_ENV === 'development' ||
                             typeof window !== 'undefined' && window.location.hostname === 'localhost';

        if (isDevelopment) {
          console.log('🧪 [DEVELOPMENT] Attempting to create mock printers...');
          await this.createDevelopmentPrinters();
          this.printers = await this.getPrinters();

          if (this.printers.length === 0) {
            return {
              success: false,
              error: 'No printers available. Please create development printers or add real printers in Settings.'
            };
          }
        } else {
          return {
            success: false,
            error: 'No printers configured. Please add a kitchen printer in Settings.'
          };
        }
      }
      
      // 🎯 CRITICAL FIX: Add order to queue system before printing
      console.log(`🔄 [printKitchenOrder] Adding order ${order.id} to queue system...`);
      await this.addOrderToQueueSystem(order);
      
      const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

      let result: PrintResult;
      switch (this.currentSystem) {
        case 'single':
          result = await this.printSingleSystem(order, tableId, options);
          break;
        case 'multi-station':
          result = await this.printMultiStationSystem(order, tableId, options);
          break;
        case 'multi-barcode':
          result = await this.printMultiBarcodeSystem(order, tableId, options);
          break;
        default:
          result = await this.printSingleSystem(order, tableId, options);
      }

      // Only show preview if not in production Electron environment
      if (isElectron) {
        result.showPreview = false;
      }

      return result;
    } catch (error) {
      console.error('Kitchen print error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown print error'
      };
    }
  }
  
  // 🎯 NEW: Add order to queue system for proper tracking
  private async addOrderToQueueSystem(order: Order): Promise<void> {
    try {
      const stationItems = this.splitOrderByStation(order);
      
      // Add order to each station's queue
      for (const [stationId, items] of Object.entries(stationItems)) {
        if (items.length > 0) {
          console.log(`📋 [addOrderToQueueSystem] Adding order ${order.id} to station ${stationId} with ${items.length} items`);
          await kitchenQueueService.addOrderToQueue(order, stationId, items);
        }
      }
      
      console.log(`✅ [addOrderToQueueSystem] Order ${order.id} successfully added to all relevant station queues`);
    } catch (error) {
      console.error(`❌ [addOrderToQueueSystem] Error adding order ${order.id} to queue:`, error);
      // Don't throw error here - printing should continue even if queue fails
    }
  }
  
  // 🖨️ System 1: Single Central Printer
  private async printSingleSystem(
    order: Order, 
    tableId?: string, 
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    const printer = this.printers.find(p => p.status === 'online') || this.printers[0];
    if (!printer) {
      return { success: false, error: 'No printer available' };
    }
    
    const content = await this.generateSingleSystemTicket(order, tableId, options);
    
    const dailySequence = extractDailySequence(order.id);
    return {
      success: true,
      printJob: {
        title: `Kitchen Order #${dailySequence}`,
        content,
        type: 'kitchen',
        printerId: printer.id
      },
      showPreview: true
    };
  }
  
  // 🏷️ Queue Context Functions
  async getStationQueueContext(stationId: string): Promise<{ totalPendingItems: number }> {
    try {
      // Use queue operations directly for more accurate queue information
      const { getStationQueue } = await import('@/lib/db/v4/operations/queue-ops');
      const stationQueue = await getStationQueue(stationId);
      
      // Calculate total individual items in pending and in-progress orders
      const totalPendingItems = stationQueue.items
        .filter(item => item.status === 'pending' || item.status === 'in-progress')
        .reduce((sum, queueItem) => {
          return sum + queueItem.items.reduce((itemSum, orderItem) => itemSum + orderItem.quantity, 0);
        }, 0);

      console.log(`📊 [getStationQueueContext] Station ${stationId} queue:`, {
        pendingOrders: stationQueue.pendingOrders,
        inProgressOrders: stationQueue.inProgressOrders,
        totalOrders: stationQueue.totalOrders,
        totalPendingItems: totalPendingItems
      });
      
      return {
        totalPendingItems: totalPendingItems
      };
    } catch (error) {
      console.error('❌ Error getting station queue context:', error);
      return { totalPendingItems: 0 };
    }
  }

  // 📊 Get other categories info for kitchen coordination
  private async getOtherCategoriesInfo(
    allStationItems: Record<string, OrderItem[]>, 
    currentStationId: string
  ): Promise<Array<{ name: string; items: OrderItem[]; queueCount: number }>> {
    const otherCategories: Array<{ name: string; items: OrderItem[]; queueCount: number }> = [];
    
    console.log(`🔍 [getOtherCategoriesInfo] Checking other stations for current station: ${currentStationId}`);
    console.log(`🔍 [getOtherCategoriesInfo] Available printers:`, this.printers.map(p => ({ id: p.id, name: p.name, categories: p.assignedCategories })));
    console.log(`🔍 [getOtherCategoriesInfo] All station items:`, Object.keys(allStationItems).map(stationId => ({ stationId, itemCount: allStationItems[stationId]?.length || 0 })));
    
    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }
    
    // Check each printer/station (excluding current station)
    for (const printer of this.printers) {
      const stationId = printer.id;
      
      // Skip current station
      if (stationId === currentStationId) {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping current station: ${stationId}`);
        continue;
      }
      
      // Check if station has items from current order
      const hasCurrentOrderItems = allStationItems[stationId] && allStationItems[stationId].length > 0;
      
      // Get queue context for this station
      const queueContext = await this.getStationQueueContext(stationId);
      const hasQueueItems = queueContext.totalPendingItems > 0;
      
      console.log(`🔍 [getOtherCategoriesInfo] Station ${stationId} (${printer.name}):`, {
        hasCurrentOrderItems,
        currentOrderItemCount: allStationItems[stationId]?.length || 0,
        hasQueueItems,
        queueCount: queueContext.totalPendingItems
      });
      
      // Include station if it has current order items OR queue items
      if (hasCurrentOrderItems || hasQueueItems) {
        let categoryName = printer.name || stationId;
        
        // Clean up printer name (remove "Printer" suffix)
        categoryName = categoryName.replace(/ Printer$/, '');
        
        // Get real category name from menu if available
        if (menu && printer.assignedCategories?.[0]) {
          const category = menu.categories.find(c => c.id === printer.assignedCategories[0]);
          if (category) {
            categoryName = category.name;
          }
        }
        
        // Use current order items if available, otherwise empty array
        const items = allStationItems[stationId] || [];
        
        console.log(`✅ [getOtherCategoriesInfo] Adding station ${stationId} (${categoryName}) with ${items.length} current items and ${queueContext.totalPendingItems} queue items`);
        
        otherCategories.push({
          name: categoryName,
          items: items,
          queueCount: queueContext.totalPendingItems
        });
      } else {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping station ${stationId} - no current items or queue`);
      }
    }
    
    // Sort by priority: stations with current order items first, then by queue count
    otherCategories.sort((a, b) => {
      if (a.items.length > 0 && b.items.length === 0) return -1;
      if (a.items.length === 0 && b.items.length > 0) return 1;
      return b.queueCount - a.queueCount; // Higher queue count first
    });
    
    console.log(`🔍 [getOtherCategoriesInfo] Final other categories (sorted):`, otherCategories.map(c => ({ name: c.name, itemCount: c.items.length, queueCount: c.queueCount })));
    
    return otherCategories;
  }

  private async getPendingOrdersForStation(stationId: string): Promise<string[]> {
    try {
      // Import here to avoid circular dependency
      const { getPendingOrders } = await import('@/lib/db/v4/operations/order-completion-ops');
      const pendingOrders = await getPendingOrders();
      
      // Filter orders that have items for this station and are not served
      const stationOrders = pendingOrders.filter(order => {
        return order.stationItems[stationId] && 
               !Object.values(order.stationItems[stationId]).every(item => item.completed);
      });
      
      return stationOrders.map(order => order.orderId);
    } catch (error) {
      console.error('❌ Error getting pending orders for station:', error);
      return [];
    }
  }

  // 🖨️🖨️🖨️ System 2: Multi-Station Printers
  private async printMultiStationSystem(
    order: Order, 
    tableId?: string, 
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    const stationItems = this.splitOrderByStation(order);
    const printJobs: PrintJob[] = [];
    
    for (const [stationId, items] of Object.entries(stationItems)) {
      const printer = this.printers.find(p => p.id === stationId);
      if (!printer || items.length === 0) continue;
      
      const content = await this.generateMultiStationTicket(
        order, 
        items, 
        printer, 
        stationItems, 
        tableId, 
        options
      );
      
      const dailySequence = extractDailySequence(order.id);
      printJobs.push({
        title: `${printer.name} - Order #${dailySequence}`,
        content,
        type: 'kitchen',
        printerId: printer.id,
        stationName: printer.name
      });
    }
    
    // Return all print jobs for multi-station system
    return {
      success: true,
      printJobs: printJobs,
      printJob: printJobs[0], // Keep for backward compatibility
      showPreview: true
    };
  }
  
  // 🖨️🖨️🖨️📱 System 3: Multi-Station + Barcode Scanning
  private async printMultiBarcodeSystem(
    order: Order, 
    tableId?: string, 
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    const stationItems = this.splitOrderByStation(order);
    const printJobs: PrintJob[] = [];
    
    // ✅ Build per-instance statuses using the new helper so that barcode ⇔ status mapping is 1-to-1
    this.buildBarcodeItemStatuses(order);
    
    for (const [stationId, items] of Object.entries(stationItems)) {
      const printer = this.printers.find(p => p.id === stationId);
      if (!printer || items.length === 0) continue;
      
      const content = await this.generateBarcodeContent(
        order, 
        items[0].categoryId || '', 
        this.getFontSizes(options.fontSize),
        tableId
      );
      
      const dailySequence = extractDailySequence(order.id);
      printJobs.push({
        title: `${printer.name} - Order #${dailySequence}`,
        content,
        type: 'kitchen',
        printerId: printer.id,
        stationName: printer.name
      });
    }
    
    // Return all print jobs for multi-barcode system
    return {
      success: true,
      printJobs: printJobs,
      printJob: printJobs[0], // Keep for backward compatibility
      showPreview: true
    };
  }
  
  // 📊 Split Order by Station (Using REAL Categories) - IMPROVED
  private splitOrderByStation(order: Order): Record<string, OrderItem[]> {
    const stationItems: Record<string, OrderItem[]> = {};
    
    // Initialize all stations
    if (this.printers.length === 0) {
      console.warn('[KitchenPrintService] splitOrderByStation called with 0 printers.');
      return {};
    }
    this.printers.forEach(printer => {
      stationItems[printer.id] = [];
    });
    
    order.items.forEach(item => {
      // Try to find printer by real category assignment
      let assignedPrinter = null;
      
      // 1. Try by menuItemId (if it matches a category ID)
      assignedPrinter = this.printers.find(printer => 
        printer.assignedCategories.includes(item.menuItemId || '')
      );
      
      // 2. Try by categoryId if available in item
      if (!assignedPrinter && (item as any).categoryId) {
        assignedPrinter = this.printers.find(printer => 
          printer.assignedCategories.includes((item as any).categoryId)
        );
      }
      
      // 3. Try by item name keywords (smart matching for items without category info)
      if (!assignedPrinter) {
        const itemName = item.name.toLowerCase();
        assignedPrinter = this.printers.find(printer => {
          return printer.assignedCategories.some(categoryId => {
            // Check if item name contains category-related keywords
            const categoryLower = categoryId.toLowerCase();
            return itemName.includes(categoryLower) || 
                   this.isItemTypeMatch(itemName, categoryLower);
          });
        });
      }
      
      // 4. If no printer is found, throw an error
      if (!assignedPrinter) {
        throw new Error(`No printer assigned for category of item: ${item.name}. Please check your printer settings.`);
      }
      
      if (assignedPrinter) {
        stationItems[assignedPrinter.id].push(item);
      }
    });
    
    // Log the distribution for debugging
    console.log('📊 Station distribution:', Object.entries(stationItems).map(([id, items]) => ({
      station: this.printers.find(p => p.id === id)?.name,
      items: items.length,
      itemNames: items.map(i => i.name)
    })));
    
    return stationItems;
  }
  
  // 🧠 Smart item type matching (fallback for items without proper category assignment)
  private isItemTypeMatch(itemName: string, categoryId: string): boolean {
    const itemNameLower = itemName.toLowerCase();
    const categoryLower = categoryId.toLowerCase();
    
    // 🎯 Primary: Direct name matching
    if (itemNameLower.includes(categoryLower) || categoryLower.includes(itemNameLower)) {
      return true;
    }
    
    // 🎯 Secondary: Flexible keyword matching (reduced hardcoding)
    // Only use as last resort when categoryId doesn't provide clear guidance
    const flexibleKeywords = {
      'hot': ['pizza', 'burger', 'pasta', 'soup', 'grilled'],
      'cold': ['salad', 'sandwich', 'smoothie', 'ice cream'],
      'beverage': ['coffee', 'tea', 'juice', 'soda', 'water', 'drink'],
      'dessert': ['cake', 'cookie', 'chocolate', 'sweet']
    };
    
    // Only use flexible matching if category suggests a general type
    for (const [type, keywords] of Object.entries(flexibleKeywords)) {
      if (categoryLower.includes(type)) {
        return keywords.some(keyword => itemNameLower.includes(keyword));
      }
    }

    return false;
  }
  
  // 🎯 SINGLE SOURCE OF TRUTH - Master Print Content Generator
  private async generatePrintContent(order: Order, options: {
    type: 'single' | 'multi-station' | 'barcode' | 'expo' | 'receipt';
    stationId?: string;
    tableId?: string;
    fontSize?: 'small' | 'medium' | 'large';
    printer?: PrinterConfig;
    stationItems?: OrderItem[];
    allStationItems?: Record<string, OrderItem[]>;
    currentStationIndex?: number;
    totalStations?: number;
    payment?: { method: string; received: number; change: number };
  }): Promise<string> {
    const fontSize = options.fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    const fs = fontSizes[fontSize];

    switch (options.type) {
      case 'single':
        return await this.generateSingleSystemContent(order, fs, options.tableId);
      case 'multi-station':
        return await this.generateMultiStationContent(order, options.stationId!, fs, options.tableId);
      case 'barcode':
        return await this.generateBarcodeContent(order, options.stationId!, fs, options.tableId);
      case 'expo':
        return this.generateExpoContent(order, fs, options.tableId);
      case 'receipt':
        return await this.generateReceiptContent(order, fs, options.tableId, options.payment);
      default:
        throw new Error(`Unknown print type: ${options.type}`);
    }
  }

  /**
   * Helper to generate the common HTML wrapper for all print jobs.
   * This includes the base styling, header (title, order ID, table ID, timestamp),
   * and a slot for the main content and optional order notes.
   */
  private async generatePrintHtmlWrapper(
    jobType: 'kitchen' | 'receipt' | 'expo', // New parameter to differentiate print types
    order: Order,
    fs: { header: number; bold: number; normal: number },
    mainContentHtml: string,
    options?: { // Use an options object for clarity
      tableId?: string;
      printDate?: Date;
      categoryOrStationName?: string; // Specific for kitchen tickets
      receiptPaymentInfo?: { method: string; received: number; change: number }; // Specific for receipts
    }
  ): Promise<string> {
    // 🎯 FIX: Use order creation time, not current time
    const orderCreationDate = new Date(order.createdAt);
    const dailySequence = extractDailySequence(order.id);

    let headerHtml = '';

    if (jobType === 'kitchen') {
      // 🎯 NEW COMPACT HEADER FORMAT BASED ON USER REQUIREMENTS
      const timeString = orderCreationDate.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      }); // HH:MM:SS format
      
      const dateString = orderCreationDate.toLocaleDateString('en-GB', { 
        day: '2-digit', 
        month: '2-digit' 
      }); // DD/MM format
      
      const categoryOrStationName = options?.categoryOrStationName || 'KITCHEN';
      
      headerHtml = `
      <div style="text-align: center; margin-bottom: 2px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold; line-height: 1;">${timeString} #${dailySequence}</div>
        <div style="font-size: ${fs.normal - 2}px; margin-top: 1px;">${dateString}</div>
      </div>
      `;
      
      // 🎯 FIX: Resolve table ID to table name and add order type in French
      if (options?.tableId || order.orderType) {
        let tableInfo = '';
        
        // Resolve table name if we have a tableId
        if (options?.tableId) {
          try {
            const { getTable } = await import('@/lib/db/v4/operations/table-ops');
            const table = await getTable(options.tableId);
            tableInfo = table?.name ? `TABLE ${table.name}` : `TABLE ${options.tableId}`;
          } catch (error) {
            console.warn('⚠️ Could not resolve table name:', error);
            tableInfo = `TABLE ${options.tableId}`;
          }
        }
        
        // 🎯 STANDARDIZED: Use centralized order type labels
        const orderTypeText = order.orderType ? getOrderTypeLabel(order.orderType) : '';
        
        // Combine table info and order type
        const infoLine = [tableInfo, orderTypeText].filter(Boolean).join(' • ');
        
        if (infoLine) {
          headerHtml += `<div style="font-size: ${fs.normal - 1}px; text-align: center; margin-bottom: 2px;">${infoLine}</div>`;
        }
      }
    } else if (jobType === 'receipt') {
      // For receipts, don't add any header here - it's handled by generateReceiptContent
      headerHtml = '';
    } else if (jobType === 'expo') {
      // 🎯 FIX: Also resolve table name for expo tickets
      let tableInfo = '';
      if (options?.tableId) {
        try {
          const { getTable } = await import('@/lib/db/v4/operations/table-ops');
          const table = await getTable(options.tableId);
          tableInfo = table?.name ? `Table ${table.name}` : `Table ${options.tableId}`;
        } catch (error) {
          console.warn('⚠️ Could not resolve table name for expo:', error);
          tableInfo = `Table ${options.tableId}`;
        }
      }
      
      headerHtml = `
      <div style="text-align: center; margin-bottom: 3px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold;">EXPO TICKET</div>
        <div style="font-size: ${fs.normal}px;">ORDER #${dailySequence}</div>
        ${tableInfo ? `<div style="font-size: ${fs.normal - 1}px;">${tableInfo}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    } else {
      // Fallback for any other unexpected jobType or generic title-based print (should not happen with defined types)
      headerHtml = `
      <div style="text-align: center; margin-bottom: 3px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold;">${options?.categoryOrStationName || 'ORDER'}</div>
        <div style="font-size: ${fs.normal}px;">ORDER #${dailySequence}</div>
        ${options?.tableId ? `<div style="font-size: ${fs.normal - 1}px;">TABLE ${options.tableId}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    }

    let html = `
<div style="font-family: 'Courier New', monospace; width: 58mm; max-width: 220px; margin: 0; padding: 2px; line-height: 1.1;">
  ${headerHtml}
  ${mainContentHtml}
`;

    // Only show order notes for kitchen tickets, not receipts
    if (order.notes && jobType !== 'receipt') {
      html += `
  <div style="margin-top: 3px; padding: 2px; border: 1px solid #000;">
    <div style="font-size: ${fs.normal - 1}px; font-weight: bold;">ORDER NOTES:</div>
    <div style="font-size: ${fs.normal - 1}px;">${order.notes}</div>
  </div>`;
    }

    html += `
</div>`;

    return html;
  }

  // 🍕 Render Kitchen Item (handles both regular items and custom pizzas) - ULTRA COMPACT FORMAT
  private renderKitchenItem(item: OrderItem, fs: { header: number; bold: number; normal: number }): string {
    let itemContent = '';
    
    // 🍕 Handle Custom Pizza Display - ULTRA COMPACT WITH VISUAL SEPARATION
    if (item.compositeType === 'pizza_quarters' && item.quarters && item.quarters.length > 0) {
      // Main item line: Pizza Personnalisée (Large) x2
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 1px;">${item.name}${item.size ? ` (${item.size})` : ''} x${item.quantity}</div>`;
      
      // Group quarters by pizza type to avoid redundancy
      const quarterGroups: Record<string, number> = {};
      item.quarters.forEach(quarter => {
        quarterGroups[quarter.name] = (quarterGroups[quarter.name] || 0) + 1;
      });
      
      // 🎯 VISUAL SEPARATION: Display quarters with count and special formatting
      const quarterEntries = Object.entries(quarterGroups);
      quarterEntries.forEach(([pizzaType, count], index) => {
        const isLast = index === quarterEntries.length - 1;
        const separator = isLast ? '' : ' • ';
        itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 1px; padding-left: 2px; border-left: 1px solid #000;">▸ ${count}x ${pizzaType}${separator}</div>`;
      });
    } else {
      // Regular item display - ONE LINE FORMAT: itemname (size) x quantity
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 1px;">${item.name}${item.size ? ` (${item.size})` : ''} x${item.quantity}</div>`;
    }
    
    // Show addons in compact format
    if (item.addons?.length) {
      itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 1px;">+${item.addons.map(a => a.name).join(', ')}</div>`;
    }
    
    // Show notes in compact format
    if (item.notes) {
      itemContent += `<div style="font-size: ${fs.normal - 2}px; font-weight: bold; margin-bottom: 1px;">⚠️ ${item.notes}</div>`;
    }
    
    return itemContent;
  }

  // 🍳 Generate Single System Content - ULTRA COMPACT
  private async generateSingleSystemContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    // 🎯 NEW COMPACT FORMAT: Category directly followed by items
    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }
      
      // Category header - immediate, no wasted space
      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '2px' : '0'}; margin-bottom: 1px;">${categoryName}:</div>`;
      
      // Items in compact format
      items.forEach(item => {
        mainContent += `<div style="margin-bottom: 1px; margin-left: 2px;">${this.renderKitchenItem(item, fs)}</div>`;
      });
    });

    // Pass 'kitchen' jobType and the generic 'KITCHEN' as categoryOrStationName for single system
    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🏪 Generate Multi-Station Content - ULTRA COMPACT
  private async generateMultiStationContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    let mainContent = '';
    
    categoryItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 1px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems }, 
      categoryId
    );
    
    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Calculate global item index across entire order (not per-category)
  private calculateGlobalItemIndex(order: Order, targetCategoryId: string, targetItemIndex: number): number {
    let globalIndex = 1;
    
    // Iterate through all items in order to find the global position
    for (const item of order.items) {
      for (let i = 0; i < item.quantity; i++) {
        if (item.categoryId === targetCategoryId && globalIndex === targetItemIndex) {
          return globalIndex;
        }
        globalIndex++;
      }
    }
    
    return globalIndex;
  }

  // 🎯 Generate Barcode Content - ULTRA COMPACT with per-item barcodes
  private async generateBarcodeContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    let mainContent = '';
    let globalIndex = this.calculateGlobalItemIndex(order, categoryId, 0);
    
    // 🔧 Build barcode statuses for this order if not already done
    this.buildBarcodeItemStatuses(order);
    
    // Import barcode service for barcode generation
    const { barcodeService } = await import('@/lib/services/barcode-service');
    
    categoryItems.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(barcodeId);
        
        // Create ultra-compact item block with barcode
        mainContent += `
        <div style="margin-bottom: 2px; padding: 1px; border: 1px solid #000;">
          ${this.renderKitchenItem({ ...item, quantity: 1 }, fs)}
          <div style="text-align: center; margin-top: 1px;">
            <img src="${barcodeDataURL}" style="max-width: 180px; height: 25px; display: block; margin: 0 auto;" />
            <div style="font-size: ${fs.normal - 3}px; margin-top: 1px;">${barcodeId}</div>
          </div>
        </div>`;
        globalIndex++;
      }
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems }, 
      categoryId
    );
    
    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Generate Expo Content - used for expo tickets that show overview of full order
  private async generateExpoContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // 🔧 Render items in a compact format
    const renderItems = (itemsToRender: OrderItem[], fs: { header: number; bold: number; normal: number }) => {
      return itemsToRender.map(item => {
        return `<div style="margin-bottom: 1px; font-size: ${fs.normal}px;">• ${item.name}${item.size ? ` (${item.size})` : ''} x${item.quantity}</div>`;
      }).join('');
    };

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }
      
      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '2px' : '0'}; margin-bottom: 1px;">${categoryName}:</div>`;
      mainContent += `<div style="margin-left: 4px;">${renderItems(items, fs)}</div>`;
    });

    mainContent += `
    <div style="margin-top: 4px; padding: 2px; border: 1px solid #000; text-align: center;">
      <div style="font-size: ${fs.normal}px; font-weight: bold;">READY FOR PICKUP</div>
    </div>`;

    return await this.generatePrintHtmlWrapper('expo', order, fs, mainContent, { tableId });
  }

  // 🧾 Generate Receipt Content
  private async generateReceiptContent(
    order: Order, 
    fs: { header: number; bold: number; normal: number },
    tableId?: string,
    payment?: { method: string; received: number; change: number }
  ): Promise<string> {
    let html = `
<div style="text-align: center; margin-bottom: 6px;">
  <div style="font-size: ${fs.header}px; font-weight: bold;">RESTAURANT NAME</div>
  <div style="font-size: ${fs.normal - 1}px;">123 Restaurant St</div>
  <div style="font-size: ${fs.normal - 1}px;">Phone: (*************</div>
</div>

<div style="text-align: center; margin-bottom: 4px; border-bottom: 1px solid #000; padding-bottom: 2px;">
  <div style="font-size: ${fs.bold}px;">RECEIPT</div>
  <div style="font-size: ${fs.normal}px;">Order #${extractDailySequence(order.id)}</div>
  <div style="font-size: ${fs.normal - 1}px;">${new Date(order.createdAt).toLocaleString()}</div>
</div>
`;

    // Items section
    html += `<div style="margin-bottom: 4px;">`;
    order.items.forEach(item => {
      html += `
<div style="display: flex; justify-content: space-between; font-size: ${fs.normal}px; margin-bottom: 1px;">
  <span>${item.name}${item.size ? ` (${item.size})` : ''} x${item.quantity}</span>
  <span>${(item.price * item.quantity).toFixed(2)} DA</span>
</div>`;
      
      if (item.addons && item.addons.length > 0) {
        item.addons.forEach(addon => {
          html += `
<div style="display: flex; justify-content: space-between; font-size: ${fs.normal - 1}px; margin-left: 8px; margin-bottom: 1px;">
  <span>+ ${addon.name}</span>
  <span>${addon.price.toFixed(2)} DA</span>
</div>`;
        });
      }
    });
    html += `</div>`;

    // Total section
    html += `
<div style="border-top: 1px solid #000; padding-top: 2px; margin-top: 4px;">
  <div style="display: flex; justify-content: space-between; font-size: ${fs.bold}px; font-weight: bold;">
    <span>TOTAL:</span>
    <span>${order.total.toFixed(2)} DA</span>
  </div>
</div>
`;

    // Payment info
    if (payment) {
      html += `
<div style="margin-top: 4px; font-size: ${fs.normal}px;">
  <div>Payment Method: ${payment.method.toUpperCase()}</div>
  <div>Amount Received: ${payment.received.toFixed(2)} DA</div>
  <div>Change: ${payment.change.toFixed(2)} DA</div>
</div>
`;
    }

    html += `
<div style="text-align: center; margin-top: 6px; font-size: ${fs.normal - 1}px;">
  <div>Thank you for your visit!</div>
  <div>Please come again!</div>
</div>
`;

    return await this.generatePrintHtmlWrapper('receipt', order, fs, html, { tableId, receiptPaymentInfo: payment });
  }

  // 🎟️ Generate Single System Print Ticket - COMPACT
  private async generateSingleSystemTicket(
    order: Order, 
    tableId?: string, 
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    
    // 🎯 USE THE NEW COMPACT SINGLE SYSTEM CONTENT
    const mainContent = await this.generateSingleSystemContent(order, fs, tableId);
    
    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🎟️ Generate Multi-Station Print Ticket (for actual printing)
    private async generateMultiStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    // 🎯 USE THE SAME COMPACT FORMAT AS OTHER FUNCTIONS
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;
    
    // 🎯 COMPACT ITEM LISTING - NO BOXES, DIRECT LIST
    stationItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 2px; margin-left: 4px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateMultiStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);
      
      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;
        
        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });
        
        mainContent += `</div>`;
      }
    }
    
    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎟️ Generate Barcode Station Print Ticket - ULTRA COMPACT
  private async generateBarcodeStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    // 🎯 COMPACT HEADER: Category directly
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;
    
    for (const stationItem of stationItems) {
      for (let i = 0; i < stationItem.quantity; i++) {
        // 🎯 CRITICAL FIX: Calculate exact global position for this specific item instance
        let globalItemIndex = 1;
        
        // Count all item instances that come before this one in the order
        for (const orderItem of order.items) {
          if (orderItem.id === stationItem.id) {
            // Found our item, add the current instance index
            globalItemIndex += i;
            break;
          }
          // Add all quantities from items that come before this one
          globalItemIndex += orderItem.quantity;
        }
        
        // 🎯 Use exact global index for this item instance
        const simpleBarcodeId = this.generateSimpleBarcodeId(order.id, globalItemIndex);
        
        // 🎯 Generate barcode SVG and embed it directly
        let barcodeSvg = '';
        try {
          const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(simpleBarcodeId);
          barcodeSvg = `<img src="${barcodeDataURL}" style="width: 100%; max-width: 180px; height: 25px; margin: 1px 0;" alt="" />`;
          console.log(`✅ [generateBarcodeContent] Barcode generated successfully for ${simpleBarcodeId}, data URL length: ${barcodeDataURL.length}`);
        } catch (error) {
          console.error('❌ Error generating barcode:', error);
          barcodeSvg = `<div style="text-align: center; font-size: 9px; border: 1px solid #000; padding: 1px;">${simpleBarcodeId}</div>`;
        }
        
        // 🎯 ULTRA COMPACT BARCODE ITEM
        mainContent += `<div style="margin-bottom: 2px; padding: 1px; border: 1px solid #000; margin-left: 4px;">`;
        mainContent += this.renderKitchenItem(stationItem, fs);
        mainContent += `<div style="text-align: center; margin: 1px 0;">${barcodeSvg}</div></div>`;
      }
    }

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateBarcodeStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);
      
      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;
        
        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });
        
        mainContent += `</div>`;
      }
    }
    
    // Pass through the new compact wrapper
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  private initializeItemStatuses(order: Order, stationItems: OrderItem[], stationId: string) {
    // Initialize status for each item in the order, specific to a station
    order.items.forEach(item => {
      if (item.categoryId && stationItems.some(si => si.id === item.id)) {
        const existingStatus = this.itemStatuses.get(item.id);
        if (!existingStatus) {
          this.itemStatuses.set(item.id, {
        orderId: order.id,
            itemId: item.id,
        itemName: item.name,
        status: 'pending',
            stationId: stationId,
        createdAt: new Date().toISOString()
      });
        }
      }
    });
  }

  scanItemBarcode(barcode: string): { success: boolean; message: string; shouldPrintExpo?: boolean } {
    // Direct lookup – key is the barcode itself
    const itemStatus = this.itemStatuses.get(barcode);

    if (!itemStatus) {
      return { success: false, message: 'Item not found or already completed.' };
    }

    // Mark as done
    itemStatus.status = 'done';
    itemStatus.scannedAt = new Date().toISOString();
    this.itemStatuses.set(barcode, itemStatus); // Update map

    // Check if all items for this order are done
    const allOrderItems = Array.from(this.itemStatuses.values()).filter(status => status.orderId === itemStatus.orderId);
    const pendingItemsCount = allOrderItems.filter(status => status.status === 'pending').length;

      return { 
        success: true, 
      message: `Item ${itemStatus.itemName} marked as done.`,
      shouldPrintExpo: pendingItemsCount === 0 // If no pending items, suggest printing expo ticket
    };
  }
  
  generateExpoTicket(orderId: string): PrintJob | null {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);
    
    if (!orderItems.every(status => status.status === 'done')) {
      return null;
    }
    
    const content = `
      <div class="text-base font-mono leading-tight">
        <div class="text-center font-bold text-xl mb-4 border-b-2 border-black pb-2">EXPO STATION</div>
        <div class="text-center font-bold text-lg mb-2 bg-green-200 p-2">READY FOR ASSEMBLY</div>
        <div class="text-center font-bold text-lg mb-2">ORDER #${orderId}</div>
        <div class="text-center text-sm mb-4">${new Date().toLocaleString()}</div>
        <hr class="my-2">
        
        <div class="mb-4 bg-green-50 p-3 border-2 border-green-500">
          <div class="font-bold mb-2 text-center">ALL STATION ITEMS COMPLETED</div>
          <div class="text-sm text-center">All kitchen stations have finished their items.</div>
          <div class="text-sm text-center font-bold">Order is ready for final assembly and service.</div>
        </div>
        
        <div class="mb-4">
          <div class="font-bold mb-2 border-b">COMPLETED ITEMS SUMMARY:</div>
          <div class="text-sm">Total items scanned: ${orderItems.length}</div>
          <div class="text-sm">All barcodes verified: YES</div>
          <div class="text-sm">Status: READY TO SERVE</div>
        </div>
        
        <hr class="my-2">
        <div class="text-center font-bold text-lg bg-green-600 text-white p-3">
          SERVE IMMEDIATELY
        </div>
        <div class="text-center text-xs mt-2">PRINTED: ${new Date().toLocaleString()}</div>
      </div>
    `;
    
    const dailySequence = extractDailySequence(orderId);
    return {
      title: `EXPO - ORDER #${dailySequence} READY`,
      content,
      type: 'expo'
    };
  }
  
  getOrderStatus(orderId: string): { pending: number; done: number; total: number } {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);
    
    const pending = orderItems.filter(status => status.status === 'pending').length;
    const done = orderItems.filter(status => status.status === 'done').length;
    
    return { pending, done, total: orderItems.length };
  }
  
  async printReceipt(
    order: Order,
    payment: { method: string; received: number; change: number },
    options: { fontSize?: 'small' | 'medium' | 'large'; printerId?: string } = {}
  ): Promise<PrintResult> {
    const fontSize = options.fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    const fs = fontSizes[fontSize];

    const content = await this.generateReceiptContent(order, fs, undefined, payment);

    const dailySequence = extractDailySequence(order.id);
    const printJob: PrintJob = {
      title: `Receipt - Order #${dailySequence}`,
      content: content,
      type: 'receipt',
      printerId: options.printerId || this.getReceiptPrinterId() || undefined,
    };
    
    return {
      success: true,
      printJob: printJob,
      showPreview: true
    };
  }
  
  async resetPrinters(): Promise<void> {
    await savePrinterSettings([]);
    this.printers = [];
    await this.assignRealCategoriesToPrinters().catch(error => {
      console.warn('⚠️ Could not reset and re-assign categories to printers:', error);
    });
    console.log('🖨️ Printers configuration reset.');
  }
  
  async forceRefreshPrinters(): Promise<void> {
    console.log('🔄 Forcing refresh of printers...');
    this.printers = []; // Clear existing printers
    await savePrinterSettings([]); // Clear from DB as well
    await this.assignRealCategoriesToPrinters(); // Re-assign based on current categories
    console.log('✅ Printers refreshed.');
  }

  // 🧪 DEVELOPMENT: Force create mock printers for testing
  async createDevelopmentPrinters(): Promise<PrinterConfig[]> {
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         typeof window !== 'undefined' && window.location.hostname === 'localhost';

    if (!isDevelopment) {
      console.warn('🚨 Development printers can only be created in development mode');
      return [];
    }

    console.log('🧪 [DEVELOPMENT] Force creating mock printers...');

    try {
      // Clear existing printers first
      this.printers = [];
      await savePrinterSettings([]);

      // Create new mock printers based on current menu
      await this.assignRealCategoriesToPrinters();

      // Ensure printers were created
      if (this.printers.length === 0) {
        // Fallback: create a basic mock printer
        const fallbackPrinter: PrinterConfig = {
          id: 'dev-mock-fallback',
          name: '🧪 [DEV] Fallback Mock Printer',
          status: 'online',
          assignedCategories: [],
          type: 'thermal',
          simulated: true
        };

        this.printers = [fallbackPrinter];
        await this.setPrinters(this.printers);
        console.log('🧪 Created fallback mock printer');
      }

      console.log(`✅ Created ${this.printers.length} development printers`);
      return this.printers;
    } catch (error) {
      console.error('❌ Failed to create development printers:', error);
      return [];
    }
  }

  // 🎯 VALIDATION: Check if all categories are properly assigned
  async validateCategoryAssignments(): Promise<{
    isValid: boolean;
    unassignedCategories: string[];
    assignmentReport: { categoryId: string; categoryName: string; printerId: string; printerName: string }[];
    warnings: string[];
  }> {
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();

      if (!menu.categories || menu.categories.length === 0) {
        return {
          isValid: false,
          unassignedCategories: [],
          assignmentReport: [],
          warnings: ['No menu categories found']
        };
      }

      const unassignedCategories: string[] = [];
      const assignmentReport: { categoryId: string; categoryName: string; printerId: string; printerName: string }[] = [];
      const warnings: string[] = [];

      // Check each category
      for (const category of menu.categories) {
        const assignedPrinter = this.printers.find(printer =>
          printer.assignedCategories.includes(category.id)
        );

        if (assignedPrinter) {
          assignmentReport.push({
            categoryId: category.id,
            categoryName: category.name,
            printerId: assignedPrinter.id,
            printerName: assignedPrinter.name
          });
        } else {
          unassignedCategories.push(category.id);
        }
      }

      // Check for duplicate assignments
      const categoryAssignmentCounts = new Map<string, number>();
      this.printers.forEach(printer => {
        printer.assignedCategories.forEach(categoryId => {
          categoryAssignmentCounts.set(categoryId, (categoryAssignmentCounts.get(categoryId) || 0) + 1);
        });
      });

      categoryAssignmentCounts.forEach((count, categoryId) => {
        if (count > 1) {
          const category = menu.categories.find(c => c.id === categoryId);
          warnings.push(`Category "${category?.name || categoryId}" is assigned to ${count} printers`);
        }
      });

      // Check for printers with no assignments
      const emptyPrinters = this.printers.filter(p => p.assignedCategories.length === 0);
      if (emptyPrinters.length > 0) {
        warnings.push(`${emptyPrinters.length} printer(s) have no category assignments: ${emptyPrinters.map(p => p.name).join(', ')}`);
      }

      return {
        isValid: unassignedCategories.length === 0 && warnings.length === 0,
        unassignedCategories,
        assignmentReport,
        warnings
      };
    } catch (error) {
      console.error('Error validating category assignments:', error);
      return {
        isValid: false,
        unassignedCategories: [],
        assignmentReport: [],
        warnings: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  // 🎯 TESTING: Simulate order routing to validate printer assignments
  async testOrderRouting(order: Order): Promise<{
    success: boolean;
    routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[];
    errors: string[];
  }> {
    const routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[] = [];
    const errors: string[] = [];

    try {
      for (const item of order.items) {
        let assignedPrinter = null;
        let categoryId = undefined;

        // Try to find printer by menuItemId (if it matches a category ID)
        assignedPrinter = this.printers.find(printer =>
          printer.assignedCategories.includes(item.menuItemId || '')
        );

        if (assignedPrinter) {
          categoryId = item.menuItemId;
        }

        // Try by categoryId if available in item
        if (!assignedPrinter && (item as any).categoryId) {
          categoryId = (item as any).categoryId;
          assignedPrinter = this.printers.find(printer =>
            printer.assignedCategories.includes(categoryId!)
          );
        }

        // Try by item name keywords (smart matching)
        if (!assignedPrinter) {
          const itemName = item.name.toLowerCase();
          assignedPrinter = this.printers.find(printer => {
            return printer.assignedCategories.some(catId => {
              const categoryLower = catId.toLowerCase();
              return itemName.includes(categoryLower) ||
                     this.isItemTypeMatch(itemName, categoryLower);
            });
          });
        }

        if (assignedPrinter) {
          routingReport.push({
            itemName: item.name,
            categoryId,
            assignedPrinter: assignedPrinter.name
          });
        } else {
          const error = `No printer assigned for item: ${item.name}`;
          routingReport.push({
            itemName: item.name,
            categoryId,
            error
          });
          errors.push(error);
        }
      }

      return {
        success: errors.length === 0,
        routingReport,
        errors
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown routing error';
      return {
        success: false,
        routingReport,
        errors: [errorMsg]
      };
    }
  }
  
  async addOSPrinter(osPrinter: OSPrinterInput): Promise<{ success: boolean; message: string }> {
    const existing = this.printers.find(p => p.id === osPrinter.id);
    if (existing) {
      return { success: false, message: `Printer with ID ${osPrinter.id} already exists.` };
    }

      const newPrinter: PrinterConfig = {
        id: osPrinter.id,
        name: osPrinter.name,
      ipAddress: osPrinter.ipAddress,
      status: osPrinter.status || 'online',
      assignedCategories: [], // No categories assigned by default for OS printers
      type: osPrinter.type || 'thermal', // Default to thermal
      simulated: false, // These are real OS printers
    };

    this.printers.push(newPrinter);
    await this.setPrinters(this.printers);
    return { success: true, message: `Printer ${newPrinter.name} added.` };
  }

  async removePrinter(printerId: string): Promise<{ success: boolean; message: string }> {
    const initialLength = this.printers.length;
    this.printers = this.printers.filter(p => p.id !== printerId);
    if (this.printers.length < initialLength) {
      await this.setPrinters(this.printers);
      return { success: true, message: `Printer ${printerId} removed.` };
    }
    return { success: false, message: `Printer ${printerId} not found.` };
  }

  getPrinterStatus(): { system: PrintingSystem; printers: PrinterConfig[]; totalPrinters: number } {
    return {
      system: this.currentSystem,
      printers: this.printers,
      totalPrinters: this.printers.length,
    };
  }

  // Helper: build itemStatuses for multi-barcode system (one entry per item instance)
  private buildBarcodeItemStatuses(order: Order): void {
    // Clear previous statuses related to the same order to avoid duplication if re-printed
    for (const key of Array.from(this.itemStatuses.keys())) {
      if (key.startsWith(`${order.id}-`)) {
        this.itemStatuses.delete(key);
      }
    }

    let globalIndex = 1;
    order.items.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        this.itemStatuses.set(barcodeId, {
          orderId: order.id,
          itemId: barcodeId, // Store the barcode itself for direct lookup
          itemName: item.name,
          status: 'pending',
          createdAt: new Date().toISOString()
        });
        globalIndex++;
      }
    });
  }

  // 🎯 VERIFICATION: Validate print job content and formatting
  async verifyPrintJob(printJob: PrintJob, expectedSystem: PrintingSystem): Promise<{
    isValid: boolean;
    issues: string[];
    contentAnalysis: {
      hasOrderNumber: boolean;
      hasItems: boolean;
      hasTimestamp: boolean;
      hasTableInfo: boolean;
      hasBarcodes: boolean;
      lineCount: number;
      estimatedPrintTime: number; // in seconds
    };
  }> {
    const issues: string[] = [];
    const content = printJob.content;
    const lines = content.split('\n');

    // Basic content validation
    const hasOrderNumber = /order\s*#?\d+/i.test(content);
    const hasItems = /qty|quantity|\d+x/i.test(content);
    const hasTimestamp = /\d{1,2}:\d{2}|\d{4}-\d{2}-\d{2}/.test(content);
    const hasTableInfo = /table|table\s*#?\d+/i.test(content);
    const hasBarcodes = content.includes('|||') || content.includes('▌'); // Barcode patterns

    // System-specific validation
    if (expectedSystem === 'multi-barcode' && !hasBarcodes) {
      issues.push('Multi-barcode system should include barcodes in print jobs');
    }

    if (expectedSystem === 'single' && printJob.stationName) {
      issues.push('Single system should not have station-specific print jobs');
    }

    if ((expectedSystem === 'multi-station' || expectedSystem === 'multi-barcode') && !printJob.stationName) {
      issues.push('Multi-station systems should have station names in print jobs');
    }

    // Content quality checks
    if (!hasOrderNumber) {
      issues.push('Print job missing order number');
    }

    if (!hasItems) {
      issues.push('Print job missing item information');
    }

    if (!hasTimestamp) {
      issues.push('Print job missing timestamp');
    }

    if (lines.length < 5) {
      issues.push('Print job content seems too short');
    }

    if (lines.length > 100) {
      issues.push('Print job content seems excessively long');
    }

    // Estimate print time (rough calculation)
    const estimatedPrintTime = Math.ceil(lines.length / 10); // ~10 lines per second for thermal printers

    return {
      isValid: issues.length === 0,
      issues,
      contentAnalysis: {
        hasOrderNumber,
        hasItems,
        hasTimestamp,
        hasTableInfo,
        hasBarcodes,
        lineCount: lines.length,
        estimatedPrintTime
      }
    };
  }

  // 🎯 COMPREHENSIVE SYSTEM TEST: Test all aspects of a printing system
  async comprehensiveSystemTest(system: PrintingSystem, testOrder: Order): Promise<{
    success: boolean;
    systemName: string;
    printResult: PrintResult;
    validationResult: any;
    routingResult: any;
    printJobVerifications: any[];
    overallScore: number; // 0-100
    recommendations: string[];
  }> {
    const recommendations: string[] = [];
    let score = 0;

    try {
      // Set system
      this.setSystem(system);

      // Test validation
      const validationResult = await this.validateCategoryAssignments();
      if (validationResult.isValid) score += 25;
      else recommendations.push('Fix category assignment issues');

      // Test routing
      const routingResult = await this.testOrderRouting(testOrder);
      if (routingResult.success) score += 25;
      else recommendations.push('Resolve item routing problems');

      // Test printing
      const printResult = await this.printKitchenOrder(testOrder, testOrder.tableId, { fontSize: 'medium' });
      if (printResult.success) score += 25;
      else recommendations.push('Fix print job generation issues');

      // Verify print jobs
      const printJobVerifications = [];
      const printJobs = printResult.printJobs || (printResult.printJob ? [printResult.printJob] : []);

      for (const printJob of printJobs) {
        const verification = await this.verifyPrintJob(printJob, system);
        printJobVerifications.push({
          printJob: printJob.title,
          ...verification
        });

        if (verification.isValid) score += Math.floor(25 / printJobs.length);
        else recommendations.push(`Improve print job quality for ${printJob.title}`);
      }

      // System-specific recommendations
      if (system === 'single' && printJobs.length > 1) {
        recommendations.push('Single system should generate only one print job');
      }

      if ((system === 'multi-station' || system === 'multi-barcode') && printJobs.length < 2) {
        recommendations.push('Multi-station systems should generate multiple print jobs for diverse orders');
      }

      return {
        success: score >= 75,
        systemName: system,
        printResult,
        validationResult,
        routingResult,
        printJobVerifications,
        overallScore: Math.min(100, score),
        recommendations
      };
    } catch (error) {
      return {
        success: false,
        systemName: system,
        printResult: { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
        validationResult: null,
        routingResult: null,
        printJobVerifications: [],
        overallScore: 0,
        recommendations: ['System test failed - check error logs']
      };
    }
  }
}

// Export singleton instance
export const kitchenPrintService = new KitchenPrintService();
export default kitchenPrintService;
