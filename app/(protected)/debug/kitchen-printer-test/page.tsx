"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { 
  Printer, 
  RefreshCw, 
  TestTube, 
  CheckCircle, 
  AlertTriangle, 
  Settings,
  Eye,
  Zap,
  BarChart3
} from "lucide-react";
import { kitchenPrintService, PrinterConfig, PrintJob } from '@/lib/services/kitchen-print-service';
import { getMenu } from '@/lib/db/v4/operations/menu-ops';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import AllPrintPreview from '@/app/components/print/AllPrintPreview';
import KitchenPrinterValidator from '@/components/debug/KitchenPrinterValidator';

interface TestResult {
  system: string;
  success: boolean;
  printJobs: PrintJob[];
  error?: string;
  timestamp: string;
}

interface CategoryAssignment {
  categoryId: string;
  categoryName: string;
  assignedPrinter: string | null;
  printerName: string | null;
}

export default function KitchenPrinterTestPage() {
  const [printers, setPrinters] = useState<PrinterConfig[]>([]);
  const [currentSystem, setCurrentSystem] = useState<'single' | 'multi-station' | 'multi-barcode'>('single');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [categoryAssignments, setCategoryAssignments] = useState<CategoryAssignment[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [previewOrder, setPreviewOrder] = useState<Order | null>(null);
  const { toast } = useToast();

  // Load initial data
  useEffect(() => {
    loadPrinterData();
    loadCategoryAssignments();
  }, []);

  const loadPrinterData = async () => {
    try {
      const currentPrinters = await kitchenPrintService.getPrinters();
      const system = kitchenPrintService.getSystem();
      setPrinters(currentPrinters);
      setCurrentSystem(system);
    } catch (error) {
      console.error('Error loading printer data:', error);
      toast({
        title: "Error Loading Printers",
        description: "Failed to load printer configuration",
        variant: "destructive"
      });
    }
  };

  const loadCategoryAssignments = async () => {
    try {
      const menu = await getMenu();
      if (!menu.categories) return;

      const assignments: CategoryAssignment[] = menu.categories.map(category => {
        const assignedPrinter = printers.find(printer => 
          printer.assignedCategories.includes(category.id)
        );
        
        return {
          categoryId: category.id,
          categoryName: category.name,
          assignedPrinter: assignedPrinter?.id || null,
          printerName: assignedPrinter?.name || null
        };
      });

      setCategoryAssignments(assignments);
    } catch (error) {
      console.error('Error loading category assignments:', error);
    }
  };

  const refreshPrinters = async () => {
    setIsLoading(true);
    try {
      await kitchenPrintService.forceRefreshPrinters();
      await loadPrinterData();
      await loadCategoryAssignments();
      toast({
        title: "✅ Printers Refreshed",
        description: "Mock printers recreated with current menu categories"
      });
    } catch (error) {
      toast({
        title: "Error Refreshing Printers",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const forceCreatePrinters = async () => {
    setIsLoading(true);
    try {
      const mockPrinters = await kitchenPrintService.createDevelopmentPrinters();
      await loadPrinterData();
      await loadCategoryAssignments();
      toast({
        title: "🧪 Development Printers Created",
        description: `Created ${mockPrinters.length} mock printers for testing`
      });
    } catch (error) {
      toast({
        title: "Error Creating Printers",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateTestOrder = async (): Promise<Order> => {
    const menu = await getMenu();
    const items = menu.categories?.slice(0, 3).map((category, index) => ({
      id: `test-item-${index}`,
      menuItemId: category.id, // Use category ID as menu item ID for testing
      name: `Test ${category.name} Item`,
      price: 1500 + (index * 500),
      quantity: 1,
      categoryId: category.id,
      notes: `Test item from ${category.name} category`
    })) || [];

    const orderId = `test-order-${Date.now()}`;
    return {
      _id: orderId,
      id: orderId,
      type: "order_document",
      schemaVersion: "v4.0",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tableId: "TEST-TABLE-01",
      status: "pending",
      orderType: "dine-in",
      items,
      total: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
      notes: "🧪 Test order for kitchen printer validation",
      paymentStatus: "pending",
      paymentMethod: "cash"
    };
  };

  const testPrintingSystem = async (system: 'single' | 'multi-station' | 'multi-barcode') => {
    setIsLoading(true);
    try {
      // Set the system
      kitchenPrintService.setSystem(system);
      
      // Generate test order
      const testOrder = await generateTestOrder();
      
      // Test printing
      const result = await kitchenPrintService.printKitchenOrder(testOrder, testOrder.tableId, { fontSize: 'medium' });
      
      const testResult: TestResult = {
        system,
        success: result.success,
        printJobs: result.printJobs || (result.printJob ? [result.printJob] : []),
        error: result.error,
        timestamp: new Date().toISOString()
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 results
      
      if (result.success) {
        toast({
          title: `✅ ${system.toUpperCase()} Test Successful`,
          description: `Generated ${testResult.printJobs.length} print job(s)`
        });
      } else {
        toast({
          title: `❌ ${system.toUpperCase()} Test Failed`,
          description: result.error || "Unknown error",
          variant: "destructive"
        });
      }
    } catch (error) {
      const testResult: TestResult = {
        system,
        success: false,
        printJobs: [],
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString()
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      
      toast({
        title: `❌ ${system.toUpperCase()} Test Error`,
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testAllSystems = async () => {
    setIsLoading(true);
    try {
      await testPrintingSystem('single');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Brief delay
      await testPrintingSystem('multi-station');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Brief delay
      await testPrintingSystem('multi-barcode');
      
      toast({
        title: "🎯 All Systems Tested",
        description: "Check results below for detailed analysis"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const showPreviewForSystem = async (system: 'single' | 'multi-station' | 'multi-barcode') => {
    try {
      kitchenPrintService.setSystem(system);
      const testOrder = await generateTestOrder();
      setPreviewOrder(testOrder);
      setShowPreview(true);
    } catch (error) {
      toast({
        title: "Preview Error",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    }
  };

  const getSystemBadgeColor = (system: string) => {
    switch (system) {
      case 'single': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'multi-station': return 'bg-green-100 text-green-800 border-green-200';
      case 'multi-barcode': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold flex items-center gap-2">
          🧪 Kitchen Printer Testing Suite
        </h1>
        <div className="flex gap-2">
          <Button onClick={refreshPrinters} disabled={isLoading} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Printers
          </Button>
          <Button onClick={forceCreatePrinters} disabled={isLoading} variant="outline">
            <TestTube className="h-4 w-4 mr-2" />
            Force Create Printers
          </Button>
          <Button onClick={testAllSystems} disabled={isLoading}>
            <TestTube className="h-4 w-4 mr-2" />
            Test All Systems
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="printers">Printers</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Printer className="h-4 w-4" />
                  Active Printers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{printers.length}</div>
                <p className="text-xs text-muted-foreground">
                  {printers.filter(p => p.simulated).length} simulated, {printers.filter(p => !p.simulated).length} real
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Current System
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Badge className={getSystemBadgeColor(currentSystem)}>
                  {currentSystem.toUpperCase()}
                </Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Active printing configuration
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Category Coverage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categoryAssignments.filter(c => c.assignedPrinter).length}/{categoryAssignments.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Categories assigned to printers
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="printers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Printer className="h-5 w-5" />
                Printer Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {printers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Printer className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No printers configured</p>
                    <Button onClick={refreshPrinters} className="mt-2">
                      Create Mock Printers
                    </Button>
                  </div>
                ) : (
                  printers.map((printer) => (
                    <div key={printer.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{printer.name}</h3>
                          {printer.simulated && (
                            <Badge variant="outline" className="text-xs">
                              🧪 Mock
                            </Badge>
                          )}
                          <Badge
                            variant={printer.status === 'online' ? 'default' : 'secondary'}
                            className={printer.status === 'online' ? 'bg-green-600' : ''}
                          >
                            {printer.status}
                          </Badge>
                        </div>
                        <Badge variant="outline">{printer.type}</Badge>
                      </div>

                      <div className="text-sm text-muted-foreground mb-2">
                        ID: {printer.id}
                        {printer.ipAddress && ` • IP: ${printer.ipAddress}`}
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {printer.assignedCategories.length === 0 ? (
                          <Badge variant="outline" className="text-xs opacity-50">
                            No categories assigned
                          </Badge>
                        ) : (
                          printer.assignedCategories.map((categoryId) => {
                            const assignment = categoryAssignments.find(c => c.categoryId === categoryId);
                            return (
                              <Badge key={categoryId} variant="secondary" className="text-xs">
                                {assignment?.categoryName || categoryId}
                              </Badge>
                            );
                          })
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Category Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {categoryAssignments.map((assignment) => (
                  <div key={assignment.categoryId} className="flex items-center justify-between p-2 border rounded">
                    <span className="font-medium">{assignment.categoryName}</span>
                    {assignment.assignedPrinter ? (
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        {assignment.printerName}
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-orange-600 border-orange-200">
                        Unassigned
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {(['single', 'multi-station', 'multi-barcode'] as const).map((system) => (
              <Card key={system}>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    {system === 'single' && '🖨️'}
                    {system === 'multi-station' && '🏭'}
                    {system === 'multi-barcode' && '📊'}
                    {system.replace('-', ' ').toUpperCase()}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-muted-foreground">
                    {system === 'single' && 'One printer handles all orders'}
                    {system === 'multi-station' && 'Categories assigned to different printers'}
                    {system === 'multi-barcode' && 'Multi-station with barcode tracking'}
                  </div>

                  <div className="flex flex-col gap-2">
                    <Button
                      onClick={() => testPrintingSystem(system)}
                      disabled={isLoading}
                      className="w-full"
                    >
                      <TestTube className="h-4 w-4 mr-2" />
                      Test System
                    </Button>

                    <Button
                      onClick={() => showPreviewForSystem(system)}
                      variant="outline"
                      className="w-full"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Button onClick={testAllSystems} disabled={isLoading}>
                  <TestTube className="h-4 w-4 mr-2" />
                  Test All Systems
                </Button>
                <Button onClick={refreshPrinters} disabled={isLoading} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Printers
                </Button>
                <Button onClick={forceCreatePrinters} disabled={isLoading} variant="outline">
                  <TestTube className="h-4 w-4 mr-2" />
                  Force Create Printers
                </Button>
                <Button
                  onClick={() => kitchenPrintService.resetPrinters()}
                  disabled={isLoading}
                  variant="outline"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Reset Configuration
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <KitchenPrinterValidator />
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No test results yet</p>
                  <p className="text-sm">Run some tests to see results here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge className={getSystemBadgeColor(result.system)}>
                            {result.system.toUpperCase()}
                          </Badge>
                          {result.success ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </span>
                      </div>

                      <div className="text-sm">
                        {result.success ? (
                          <div className="text-green-700">
                            ✅ Generated {result.printJobs.length} print job(s)
                          </div>
                        ) : (
                          <div className="text-red-700">
                            ❌ {result.error || 'Unknown error'}
                          </div>
                        )}
                      </div>

                      {result.printJobs.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {result.printJobs.map((job, jobIndex) => (
                            <Badge key={jobIndex} variant="outline" className="text-xs">
                              {job.stationName || job.printerId}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Print Preview Dialog */}
      {showPreview && previewOrder && (
        <AllPrintPreview
          open={showPreview}
          onClose={() => setShowPreview(false)}
          order={previewOrder}
          tableId={previewOrder.tableId}
        />
      )}
    </div>
  );
}
